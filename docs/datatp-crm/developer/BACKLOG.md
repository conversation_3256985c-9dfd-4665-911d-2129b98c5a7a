---
sidebar_position: 2
hide_table_of_contents: true
displayed_sidebar: developerSidebar
---

# DataTP CRM Backlog

## Tasks

**Sprint Rules:**

- Sprint duration: 1 week (Monday-Sunday)
- Daily status updates required
- Incomplete tasks return to backlog for next sprint prioritization
- <PERSON><PERSON><PERSON> tasks làm đi làm lại, làm hỏng không note vào backlog mà tự giác fix, sửa.

## Current Sprint
1. Tasks:

1. [Dan] - Báo cáo team BD.
- Cập nhật MSA, Sync data house bill, volume, profit.
- Implement màn hình báo cáo profit theo công ty, theo loại hàng, theo agent.

2. [Dan] - Req tạo Agent.
- Chọn <PERSON>an.


2. [An] - Saleman Performance Report.
- Chức năng lọc, xuất excel (ở User/ Company/ Admin)

3. [Nhat] - Test chức năng tạo Partner, request tạo BFSOne Partner.
  - Thêm màn hình list Co-Loader, b<PERSON> sung các chức năng tương tự Customer/Agent

## Backlog

1. <PERSON><PERSON><PERSON> lại thông tin bfsone partner, bổ sung thông tin customerTypeId.


1. Req by <PERSON><PERSON> (BD) - Cập nhật thêm form bảng giá cho BD.
- Rút gọn thông tin Local Charge.
- Tách các thông tin cước.
- Có tuỳ chọn để included thông tin KB, Costing vào note ở selling lúc gửi IB.

5. [An] - Feedback Inquiry hàng rời.
```
a note lại 1 số những sửa đổi như này nhé:
8. thêm 1 cột đặt thời gian remind sale feedback, lặp remind (theo từng giờ, theo ngày)
9. thêm 1 cột yêu cầu deadline sale phải phản hồi, nội dung này sẽ đi kèm khi mà a báo giá trên CRM
10. tạo hệ thống chấm điểm feedback của sale, để đánh giá sự tích cực phản hồi của sale
11. lọc các inquiry theo tiêu chí hàng nhập, xuất, theo khu vực địa lý
****Các báo cáo cần tạo:
1. tỷ trọng các inquiry theo văn phòng theo tháng, lọc theo ngày nhập inquiry
2. tỷ trọng các loại inquiry : FIXED, NM, NR, NF, checking, tính từ inquiry đầu tiên lọc tới ngày cuối tháng báo cáo và trừ đi những inquiry DROPED tới ngày cuối cùng của tháng trước.
3. tỷ trọng hàng nhập theo vùng, hàng xuất theo vùng, lọc theo ngày nhập inquiry
```

6. [An] - Feedback Inquiry, Bảng giá Trucking

4. Req by Mr. Minh Sale - Chỉnh sửa lại UI Task Calendar, Task Customer.
  ```
    Excel báo cáo performance sale.
      Cào bằng, tổng hợp dữ liệu toàn bộ sales export ra file excel.
  ```

4. Dashboard Sale - Bản đồ khách hàng.
- Idea:
    Nhìn được xem BEE phủ được bao % ở các khu vực rồi.
    ```
    ý a là ví du Đình Vũ có 1000 công ty ( đếm Customser và LEAD )
      Bee đang làm dc 500 rồi ( Customers )
      và 500 LEAD ( chưa win )
      -> phủ được 50%
    ```

6. [An] - Tạo Agents bằng file excel.
  - Lúc làm nhắn Đàn để lấy template.

2. Thông báo những giá mới được update/đang promote. #pending
  - Theo dõi lịch sử search giá, gửi báo giá theo các tuyến của saleman, theo dõi lịch sử cập nhật, chỉnh sửa giá của pricing.
    Từ đó, gửi lại thông tin giá cho salesman (giá tăng, giá giảm so với giá gần nhất, người cập nhật, số lượng cập nhật, chi tiết một số giá mẫu, ...)
  - Ở màn hình tìm kiếm giá:
    - Từng line giá nên có icon theo chiều hướng lên/ xuống để thể hiện giá đó đang tăng hay giảm so với lần cập nhật trước hoặc trung bình cộng của kỳ trước (theo rule setup).
    - Viết cron cập nhật vào 8h hàng ngày.
      - Tạo thêm trường ở bảng giá FCL để thể hiện giá tăng/ giảm (%) so với lần cập nhật trước.
      - Cron sẽ chạy vào 8h hàng ngày, check các giá FCL mới nhất, nếu giá > giá cũ thì update field trên.

  - Tạo thêm một màn hình tổng quan về việc cập nhật giá theo các tuyến của pricing (tuyến nào, giá nào, ngày cập nhật, ai cập nhật, ...)
    để sales có thể theo dõi các giá mới cập nhật trên phần mềm.

5. Partner Lost. `PENDING`
    ```
    ví dụ : có 1 customers ( gọi là CS ) nhưng k làm với BEE nữa.
    Giờ các sale tra thấy quá thời gian -> Auto tiếp cận. Như cái CRM Cũ thì có MST rồi k tạo đc LEAD hay gì
    nên cũng k thể biết là sale tiếp cận bi trùng k ấy e.
    ```
    => Lead/ CS nếu quá hạn thì để có thể xem được (có thể set để TBP xem) => xong tự allocate cho từng sales phù hợp, hoặc sales tự req để xin.

1. - Thêm bảng Agent Transactions cho BD:
    + Click vào từng agent thì sẽ sổ ra các file nomi, freehand đã làm với agent, xem được các thông tin cơ bản như POL, POD, Salesman, Source, Volume (như trên BFS or file excel đính kèm);
    + Viết docs giới thiệu

-----------------------------------------------
### Planning:
1. Implement DataTPServiceClient -> ServiceCall
- Enhance service để các app, module khác sử dụng gửi mail/ zalo qua api. (Thay vì gọi thẳng service như hiện tại)
- Tách logic, code, lib để communication như một microservice độc lập.

2. Keycloak SSO cho DataTP CRM.
 - Ngiên cứu, tích hợp vào app DataTP CRM, discuss với Đạt Lương.

3. Micro Frontend cho DataTP CRM.

4. Mail ticket: Nghiên cứu, tìm giải pháp, case study.



