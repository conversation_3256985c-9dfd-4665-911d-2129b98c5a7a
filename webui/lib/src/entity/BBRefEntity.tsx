import React, { KeyboardEvent } from 'react';
import * as FeatherIcon from 'react-feather'

import { Validator } from 'util/validator';
import { BackendResponse } from 'server';
import {
  mergeCssClass,
  Toolbar, Button, Scrollable, Grid, GridConfig,
  CssTooltip, CssTooltipToggle, CssTooltipContent,
  PopoverTemplate, PopoverTemplateProps,
  dialogMessage
} from "../wbootstrap/core";
import * as grid from "../wbootstrap/grid";
import * as sql from "../wbootstrap/sql";
import { InputObserver } from "../wbootstrap/input";
import { AppContext, PageContext } from "../app/app";
import { AppComponent, AppComponentProps } from "../app/component/core";

import { DbEntityListPlugin, DbEntityList, DbEntityListProps } from './DbEntityList'
import { ExcludeRecordIdFilter, RecordFilter } from './EntityList';
import { DbEntityListConfigTool } from './DbEntityConfigTool'

type MapInputChange = 'success' | 'fail' | 'nochange';

export interface BBRefEntityPluginConfig {
  backend: {
    context: 'system' | 'company',
    service: string;
    searchMethod: string;
    createSearchParams?: (origParams: sql.SqlSearchParams, userInput: string) => any;
    loadMethod?: string;
    createLoadParams?: (idValue: any) => any;
  },
  bean: {
    idField: string;
    labelField: string;
    mapSelect?: (ui: BBRefEntity, bean: any, selectOpt: any, idValue: any, labelValue: any) => MapInputChange;
    renderRefEntityInfoTooltip?: (bean: any) => any,
  },
  refEntity: {
    multiselect?: boolean;
    idField: string;
    labelField: string;
    labelFunc?: (record: any) => any;
    vgridRecordConfig: grid.RecordConfig
  }
}

export class UIRefEntityListPlugin extends DbEntityListPlugin {
  config: BBRefEntityPluginConfig;

  constructor(config: BBRefEntityPluginConfig) {
    super([]);
    this.config = config;
    this.backend = {
      context: config.backend.context,
      service: config.backend.service,
      searchMethod: config.backend.searchMethod
    }
    this.searchParams = {
      "filters": sql.createSearchFilter(),
      "maxReturn": 250
    }
    if (config.backend.createSearchParams) {
      this.searchParams = config.backend.createSearchParams(this.searchParams, '');
    }
  }

  loadData(uiList: DbEntityList<any>) {
    this.createBackendSearch(uiList, { 'params': this.searchParams }).call()
  }
}

interface UIRefEntityListProps extends DbEntityListProps {
}
export class UIRefEntityList extends DbEntityList<UIRefEntityListProps> {
  createVGridConfig() {
    let { plugin } = this.props;
    let pluginImpl = plugin as UIRefEntityListPlugin;
    let config = pluginImpl.config;
    let refEntityConfig = config.refEntity;
    let footer: Record<string, grid.VGridFooterConfig> | undefined = undefined;
    if (refEntityConfig.multiselect) {
      footer = {
        page: {
          render: (_ctx: grid.VGridContext) => {
            let html = (
              <Toolbar className='border'>
                <Button laf='secondary' outline icon={FeatherIcon.Plus} onClick={() => this.onMultiSelect()}>
                  <FeatherIcon.Check size={12} /> Select
                </Button>
              </Toolbar>
            );
            return html;
          }
        }
      };
    }
    let gridConfig: grid.VGridConfig = {
      title: 'Select..',
      record: config.refEntity.vgridRecordConfig,
      toolbar: {
        actions: [],
        filters: DbEntityListConfigTool.TOOLBAR_FILTERS(plugin.searchParams ? true : false)
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
      footer: footer
    };
    return gridConfig;
  }

  onSelect(dRecord: grid.DisplayRecord) {
    let { appContext, pageContext, onSelect } = this.props;
    pageContext.back();
    if (onSelect) {
      onSelect(appContext, pageContext, dRecord.record);
    }
  }
}

interface UIRefEntityInfoProps extends AppComponentProps {
  config: BBRefEntityPluginConfig;
  refEntity: any;
}

class UIRefEntityInfo extends AppComponent<UIRefEntityInfoProps> {
  refEntitInfoGrid: GridConfig;

  constructor(props: UIRefEntityInfoProps) {
    super(props);
    this.refEntitInfoGrid = {
      showHeader: true,
      showBorder: true,
      columns: [
        { field: 'label', label: 'Label', width: 200, cssClass: 'fw-bold' },
        { field: 'value', label: 'Value', cssClass: 'align-items-end' },
      ]
    }
  }

  render() {
    let { refEntity, config } = this.props;
    let fields = config.refEntity.vgridRecordConfig.fields;
    let beans: Array<any> = [];
    for (let field of fields) {
      beans.push(
        { label: field.label, value: refEntity[field.name] }
      )
    }
    let html = (<Grid config={this.refEntitInfoGrid} beans={beans} />);
    return html;
  }

}

export class BBRefEntityPlugin {
  config: BBRefEntityPluginConfig;

  constructor(config: BBRefEntityPluginConfig) {
    this.config = config;
  }

  searchOptions(ui: BBRefEntity, userInput: string, onChangeCallback: (options: Array<any>) => void): void {
    let { appContext } = ui.props;
    let backend = this.config.backend;
    userInput = userInput.trim();
    userInput = `${userInput}*`
    let searchParams: sql.SqlSearchParams = {
      "filters": [
        { "name": "search", 'fields': [], 'filterOp': 'ilike', "filterValue": userInput, "required": true }
      ],
      maxReturn: 100
    }
    let userParams = searchParams;
    if (backend.createSearchParams) {
      userParams = backend.createSearchParams(searchParams, userInput);
    }
    if (userParams == searchParams) {
      userParams = { "params": searchParams }
    }
    appContext
      .createBackendCall(backend.service, backend.searchMethod, userParams)
      .withSuccessData((entities: Array<any>) => {
        let recordFilter = ui.createExcludeRecordFilter();
        entities = recordFilter.filter(entities);
        onChangeCallback(entities);
      })
      .call();
  }

  mapSelect(ui: BBRefEntity, bean: any, selectOpt: any, idValue: any, labelValue: any): MapInputChange {
    let beanConfig = this.config.bean;
    if (beanConfig.mapSelect) {
      return beanConfig.mapSelect(ui, bean, selectOpt, idValue, labelValue);
    }
    if (selectOpt) {
      bean[beanConfig.idField] = idValue;
      bean[beanConfig.labelField] = labelValue;
      return 'success';
    } else {
      bean[beanConfig.idField] = null;
      bean[beanConfig.labelField] = null;
      const { allowUserInput } = ui.props;
      if (allowUserInput) {
        bean[beanConfig.labelField] = labelValue;
        return 'success';
      } else {
        return 'fail';
      }
    }
  }

  mapInputChange(ui: BBRefEntity, selectOpt: null | any, oldVal: string, userInput: string) {
    const { bean, inputObserver, onPostUpdate } = ui.props;
    let model = ui.state.model;
    if (userInput && userInput.trim) userInput = userInput.trim();
    let mapInputChange: MapInputChange = 'nochange';
    if (selectOpt) {
      let refEntityConfig = this.config.refEntity;
      let idValue = selectOpt[refEntityConfig.idField];
      let labelValue = selectOpt[refEntityConfig.labelField];
      mapInputChange = this.mapSelect(ui, bean, selectOpt, idValue, labelValue);
      if (mapInputChange = 'success') {
        model.resetWithValue(labelValue);
      } else {
        model.resetWithValue("");
      }
    } else {
      mapInputChange = this.mapSelect(ui, bean, null, null, userInput);
      if (mapInputChange == 'success') {
        model.resetWithValue(userInput);
      } else {
        model.resetWithValue("");
      }
    }
    if (mapInputChange && inputObserver) {
      let field = this.config.bean.labelField;
      inputObserver.getInputChangeCollector().addChange(field, oldVal, userInput);
    }
    //if (mapInputChange == 'success' && onPostUpdate) {
    if (onPostUpdate) {
      onPostUpdate(ui, bean, selectOpt || {}, userInput);
    }
    ui.forceUpdate();
  }

  addRefEntity(ui: BBRefEntity, selectOpt: any, userInput: string) {
    const { bean, inputObserver, onPostUpdate } = ui.props;
    let model = ui.state.model;
    let refEntityHolder = bean as Array<any>;
    refEntityHolder.push(selectOpt);
    model.resetWithValue('');
    if (inputObserver) {
      let field = this.config.refEntity.labelField;
      inputObserver.getInputChangeCollector().addChange(field, '', userInput);
    }
    if (onPostUpdate) {
      onPostUpdate(ui, bean, selectOpt, userInput);
    }
    ui.forceUpdate();
  }

  allowCreateNew(_WAutoComplete: BBRefEntity) { return false; }
  onCreateNew(_WAutoComplete: BBRefEntity) { return null; }

  onKeyDown(_ui: BBRefEntity, _evt: KeyboardEvent) { return false; }
}

class BBRefEntityStateModel {
  input: string;
  origValue: string;
  options: Array<any> = [];
  currSelect: number = -1;
  focus: boolean = false;
  validated: boolean = false;
  errorMessage: string = '';

  constructor(config: BBRefEntityPluginConfig, bean: any) {
    let field = config.bean.labelField;
    let value = bean[field];
    value = value ? value : '';
    this.resetWithValue(`${value}`);
  }

  withOptions(options: Array<any>) {
    this.options = options;
    this.currSelect = options.length > 0 ? 0 : -1;
  }

  onFocusLost() {
    this.focus = false;
    this.currSelect = -1;
  }

  onFocus() {
    this.focus = true;
    this.errorMessage = '';
  }

  validate() {
    this.validated = true;
    this.errorMessage = '';
  }

  onError(mesg: string) {
    this.input = '';
    this.currSelect = -1;
    this.validated = false;
    this.errorMessage = mesg;
  }

  resetWithValue(value: any) {
    this.origValue = value;
    this.input = value ? value : '';
    this.errorMessage = '';
    this.validated = !(!value || value === '');
  }
}

export interface BBRefEntityProps extends PopoverTemplateProps {
  appContext: AppContext;
  pageContext: PageContext;
  label?: string;
  placeholder: string;
  autofocus?: boolean;
  tabIndex?: number;
  disable?: boolean;
  hideMoreInfo?: boolean;
  bean: any;
  allowUserInput?: boolean;
  validators?: Array<Validator>,
  inputObserver?: InputObserver;
  required?: boolean;
  onPostUpdate?: (inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => void
};
interface RefEntityState {
  model: BBRefEntityStateModel;
};
export class BBRefEntity<T extends BBRefEntityProps = BBRefEntityProps> extends PopoverTemplate<T, RefEntityState> {
  plugin: BBRefEntityPlugin;
  autoCompleteInput: any;
  mouseSelect = false;

  static getDerivedStateFromProps(_props: BBRefEntityProps, state: RefEntityState) {
    return state;
  }

  constructor(props: T) {
    super(props);
    let plugin = this.createPlugin()
    this.onPostCreatePlugin(plugin);
    this.autoCompleteInput = React.createRef();
    this.state = { model: this.createBBRefEntityStateModel(props, plugin) };
    this.plugin = plugin;
  }

  createBBRefEntityStateModel(props: T, plugin: BBRefEntityPlugin) {
    return new BBRefEntityStateModel(plugin.config, props.bean);
  }

  protected createPlugin(): BBRefEntityPlugin {
    throw new Error("You have to override this method")
  }

  protected onPostCreatePlugin(_plugin: BBRefEntityPlugin) {
  }

  onFocus = (evt: any) => {
    if (this.props.disable) return;
    evt.target.select();
    evt.stopPropagation();
    let { model } = this.state;
    model.onFocus();
    this.mouseSelect = false;
  }

  onFocusLost = (_evt: any) => {
    let { disable } = this.props;
    if (disable) return;
    let thisUI = this;
    let callback = () => {
      /**
       * If select item by mouse from the drop down menu, mouse select will click outside of input and
       * cause a focus lost
       * */
      if (this.mouseSelect) {
        this.mouseSelect = false;
        return;
      }
      let { model } = thisUI.state;
      this.popoverHide();
      if (thisUI.props.allowUserInput) {
        let userInput = model.input.trim();
        let origValue = model.origValue.trim();
        if (userInput != origValue) {
          this.plugin.mapInputChange(this, null, origValue, userInput);
        }
        model.onFocusLost();
      } else {
        model.onFocusLost()
        if (!model.validated) {
          this.plugin.mapInputChange(this, null, model.origValue, '');
        }
      }
    }
    /** make sure this callback time is longer than onSelection() callback */
    setTimeout(callback, 200);
  }

  /**
   * 1. onKeyDown is called before onChange
   * 2. onChange won't be called for certain key such ENTER, ESC...
   */

  onKeyDown = (evt: KeyboardEvent) => {
    this.onKeyDownDefault(evt);
  }


  protected onKeyDownDefault(evt: KeyboardEvent) {
    let { disable } = this.props;
    if (disable) return;
    if (this.plugin.onKeyDown(this, evt)) return;

    let keyCode = evt.key;
    if (evt.shiftKey || evt.ctrlKey) return;
    const { model } = this.state;

    if (keyCode === 'ArrowUp') {
      evt.stopPropagation();
      if (model.currSelect - 1 >= 0) {
        model.currSelect -= 1;
        this.forceUpdate();
      }
    } else if (keyCode === 'ArrowDown') {
      evt.stopPropagation();
      let max = model.options.length;
      if (model.currSelect + 1 < max) {
        model.currSelect += 1;
        this.forceUpdate();
      } else if (model.currSelect + 1 == max && this.plugin.allowCreateNew(this)) {
        model.currSelect += 1;
        this.forceUpdate();
      }
    } else if (keyCode === 'Escape') {
      this.popoverHide();
      evt.stopPropagation();
      model.input = '';
      model.currSelect = -1;
      model.validated = false;
      this.forceUpdate();
    } else if (keyCode === 'Enter') {
      evt.stopPropagation();
      this.onSelectOption(model.currSelect);
    }
  }

  onInputChange = (evt: any) => {
    let userInput = evt.target.value;
    const { model } = this.state;
    this.onDefaultInputChange(model, userInput);
    this.setState({ model: model })
  }

  onDefaultInputChange(model: BBRefEntityStateModel, userInput: any) {
    model.input = userInput;
    model.validated = false;
    let onChangeCallback = (options: Array<any>) => {
      model.withOptions(options);
      this.popoverShow();
      this.forceUpdate();
    }
    this.plugin.searchOptions(this, userInput, onChangeCallback);
  }

  onSelectOption(idx: number) {
    const { model } = this.state;
    let { allowUserInput } = this.props;
    this.popoverHide();
    let onSelectCallback = () => {
      let max = model.options.length;
      if (idx < 0) {
        let userInput = '';
        if (allowUserInput) userInput = model.input;
        this.plugin.mapInputChange(this, null, model.origValue, userInput);
      } else if (idx == max && this.plugin.allowCreateNew(this)) {
        this.plugin.onCreateNew(this);
        model.input = '';
        model.options = [];
        this.forceUpdate();
        return;
      } else {
        let selectOpt = model.options[idx];
        this.plugin.mapInputChange(this, selectOpt, model.origValue, model.input);
      }

      if (this.autoCompleteInput.current) {
        model.onFocus();
        this.autoCompleteInput.current.focus();
      }
    }
    setTimeout(onSelectCallback, 50);
  }

  onSelectRefEntityCallback = (_appCtx: AppContext, _pageCtx: PageContext, selEntity: any) => {
    const { model } = this.state;
    this.plugin.mapInputChange(this, selEntity, model.origValue, '');
  }

  onSelectMultiRefEntitiesCallback = (_appCtx: AppContext, _pageCtx: PageContext, _entities: Array<any>) => {
  }

  onMouseSelectOption(idx: number) {
    /**
     * Mouse selecttion will click outside of input and trigger a focus lost.
     * Make sure update value call once , since onFocusLost will try to validate and update value as well
     */
    this.mouseSelect = true;
    this.onSelectOption(idx);
  }

  onMouseHoverOption(event: React.MouseEvent<HTMLDivElement>, idx: number) {
    /**
     * Handles the mouse hover over an option.
     * This method updates the current selection index in the model to reflect
     * the option that the user is currently hovering over with the mouse. It
     * then forces a component update to visually indicate the hover state.
     *
     */
    const { model } = this.state;
    model.currSelect = idx;

    const target = (event.target as HTMLElement).closest('.option') as HTMLDivElement;
    if (target) {
      const parent = target.parentElement;
      if (parent) {
        const activeSiblings = parent.querySelectorAll('.option.active');
        activeSiblings.forEach((sibling) => {
          sibling.classList.remove('active');
          sibling.classList.remove('py-2')
        });
      }
      target.classList.add('active');
      target.classList.add('py-2');
    }
    //this.forceUpdate();
  }

  onMouseLeaveOut(event: React.MouseEvent<HTMLDivElement>) {
    /**
     * Handles the mouse leaving an option.
     * This method resets the current selection index in the model to indicate
     * that no option is currently being hovered over.
     */
    const { model } = this.state;
    model.currSelect = -1; // Reset the selection index
    const target = (event.target as HTMLElement).closest('.option') as HTMLDivElement;
    if (target) {
      target.classList.remove('active');
      target.classList.remove('py-2');
    }
    //this.forceUpdate();
  }

  onShowRefEntityInfo = () => {
    let backend = this.plugin.config.backend
    if (!backend.loadMethod) {
      throw new Error(`the load method is not config.`)
    }
    let { appContext, pageContext, bean: bean } = this.props;
    let idValue = bean[this.plugin.config.bean.idField];
    if (!idValue || idValue <= 0) {
      let html = (<div>The id is not set</div>)
      dialogMessage('Ref Entity', 'warning', html);
      return;
    }
    let successCallback = (resp: BackendResponse) => {
      let refEntity = resp.data;
      let createAppPage = (appCtx: AppContext, pageCtx: PageContext) => {
        return (
          <UIRefEntityInfo appContext={appCtx} pageContext={pageCtx} config={this.plugin.config} refEntity={refEntity} />
        )
      }
      pageContext.createPopupPage('ref-entity', 'Ref Entity', createAppPage, { size: 'md' });
    }
    let params = { 'id': idValue };
    if (backend.createLoadParams) {
      params = backend.createLoadParams(idValue);
    }
    appContext
      .createBackendCall(backend.service, backend.loadMethod, params)
      .withSuccess(successCallback)
      .call();
  }

  createExcludeRecordFilter(): RecordFilter {
    let entityRefConfig = this.plugin.config.refEntity;
    let excludeEntities: Array<any> = [];
    if (entityRefConfig.multiselect) {
      let { bean } = this.props;
      excludeEntities = bean as Array<any>;
    }
    return new ExcludeRecordIdFilter(excludeEntities);
  }

  onSearchRefEntities = () => {

    let excludeRecordFilter = this.createExcludeRecordFilter();
    let plugin = new UIRefEntityListPlugin(this.plugin.config).withRecordFilter(excludeRecordFilter);

    let createAppPage = (appCtx: AppContext, pageCtx: PageContext) => {
      return (
        <UIRefEntityList
          appContext={appCtx} pageContext={pageCtx} plugin={plugin}
          onSelect={this.onSelectRefEntityCallback} onMultiSelect={this.onSelectMultiRefEntitiesCallback} />
      )
    }
    let { pageContext } = this.props;
    pageContext.createPopupPage('ref-entity', 'Search Ref Entities', createAppPage, { size: 'lg' });
  }

  renderOptions(options: Array<any>) {
    let refRecord = this.plugin.config.refEntity;
    let idField = refRecord.idField;
    let labelField = refRecord.labelField;
    let labelFieldFunc = refRecord.labelFunc;
    const { model } = this.state;
    let optionHtml: JSX.Element[] = [];
    let max = options.length;
    for (let i = 0; i < max; i++) {
      let opt = options[i];
      let identifier = opt[idField];
      let label = opt[labelField];
      if (labelFieldFunc) {
        label = labelFieldFunc(opt);
      }
      let itemClassName = 'dropdown-item';
      if (model.currSelect === i) {
        itemClassName = `${itemClassName} active py-2`;
      }

      optionHtml.push(
        <div key={`opt-${i}`} className={`${itemClassName} option`} style={{ cursor: 'pointer' }}
          onClick={() => this.onMouseSelectOption(i)}
          onContextMenu={(e) => e.preventDefault()}
          onMouseLeave={(e) => this.onMouseLeaveOut(e)}
          onMouseEnter={(e) => this.onMouseHoverOption(e, i)}>
          <div className='identifier'>{identifier}</div>
          <div className='desc'>{label}</div>
        </div>
      );
    }
    if (options.length > max) {
      optionHtml.push(<div key='more' className='text-center'>...</div>);
    }
    if (this.plugin.allowCreateNew(this)) {
      let itemClassName = 'dropdown-item';
      if (model.currSelect == max) {
        itemClassName = `${itemClassName} active`;
      }
      optionHtml.push(
        <div key={'new'} className={itemClassName}>
          <Button laf='link' className='px-1' onClick={(_evt) => this.onMouseSelectOption(options.length)}>
            <FeatherIcon.Plus size={12} />Create New
          </Button>
        </div>
      );
    }
    return optionHtml;
  }

  protected renderRefEntityInfo() {
    let { bean } = this.props;
    let backendConfig = this.plugin.config.backend;
    let beanConfig = this.plugin.config.bean;
    let tooltip = null;
    let styleClass = undefined;
    let idValue = bean[beanConfig.idField];

    if (idValue) {
      tooltip = `ID: ${idValue}`
    } else {
      tooltip = `ID: N/A`
      styleClass = 'text-warning'
    }

    if (beanConfig.renderRefEntityInfoTooltip) {
      tooltip = beanConfig.renderRefEntityInfoTooltip(bean);
    }
    let refEntityInfo = (
      <CssTooltip key={idValue ? idValue : 'na'} className='pe-1'>
        <CssTooltipToggle><FeatherIcon.Link className={styleClass} size={12} /></CssTooltipToggle>
        <CssTooltipContent style={{ transform: 'translate(-150px, -65px)' }}>
          {tooltip}
        </CssTooltipContent>
      </CssTooltip>
    );
    if (backendConfig.loadMethod) {
      refEntityInfo = (
        <Button laf='link' className='p-0' onClick={this.onShowRefEntityInfo}>{refEntityInfo}</Button>
      );
    }
    return refEntityInfo;
  }

  protected renderSearchRefEntities() {
    let { disable } = this.props;
    let html = (
      <div className='pe-1'>
        {!disable ?
          <Button tabIndex={-1} laf='link' className='p-0' onClick={this.onSearchRefEntities}>
            <FeatherIcon.Search size={12} />
          </Button>
          : <></>
        }
      </div>
    )
    return html;
  }

  renderInput() {
    const { model } = this.state;
    let { style, className, disable, tabIndex, placeholder, required } = this.props;

    let displayValue = model.input;
    let classes = mergeCssClass(className, 'flex-hbox form-control');
    if (!model.validated && !model.focus) {
      displayValue = model.errorMessage;
      classes = mergeCssClass(classes, 'flex-hbox form-control-error');
    }
    if (disable) {
      classes = mergeCssClass(classes, 'flex-hbox form-control-error text-muted');
    }

    if (required && (!model.input || model.input.trim() === '')) {
      classes = mergeCssClass(classes, 'border-danger');
    }

    let inputUI = (
      <input className={classes} style={style} required={required}
        ref={this.autoCompleteInput} value={displayValue} placeholder={placeholder}
        readOnly={disable} autoComplete="off" type={'text'} tabIndex={tabIndex} autoFocus={model.focus}
        onKeyDown={this.onKeyDown} onChange={this.onInputChange} onFocus={this.onFocus} onBlur={this.onFocusLost} />
    );
    return inputUI;
  }

  renderToggle() {
    let { label, hideMoreInfo } = this.props;
    let inputUI = (
      <div className='flex-hbox' id={`popover-trigger-${this.popoverId}`}>
        {this.renderInput()}
        <div className='flex-hbox flex-grow-0 justify-content-end align-items-center'>
          {!hideMoreInfo && this.renderRefEntityInfo()}
          {this.renderSearchRefEntities()}
        </div>
      </div>
    );
    if (!label) return inputUI;
    return (
      <div className='bb-field w-100 h-100'>
        <label className='form-label'>
          {label}
        </label>
        {inputUI}
      </div>
    );
  }

  renderContent() {
    const { model } = this.state;
    const { minWidth } = this.props;
    let dropdownContent = <></>;
    if (this.popoverVisible) {
      // let contentWidth = this.autoCompleteInput.current.offsetWidth;
      // if (minWidth && contentWidth < minWidth) contentWidth = minWidth;
      // if (minWidth) contentWidth = minWidth;
      const contentWidth = minWidth || 300; //REMIND: ensure fix to popover content width.

      let dropdownContentClassName = 'autocomplete-dropdown-menu p-2';
      let computeContentStyle = { minHeight: '20px', width: contentWidth, maxHeight: 300 };
      dropdownContent = (
        <Scrollable className={dropdownContentClassName} style={computeContentStyle}>
          {this.renderOptions(model.options)}
        </Scrollable>
      );
    }
    return dropdownContent;
  }

}

export class BBRefMultiEntityPlugin extends BBRefEntityPlugin {

  mapInputChange(ui: BBRefEntity, selectOpt: null | any, oldVal: string, userInput: string) {
    const { bean, inputObserver, onPostUpdate } = ui.props;
    let model = ui.state.model;
    if (userInput && userInput.trim) userInput = userInput.trim();
    let mapInputChange: MapInputChange = 'nochange';
    if (selectOpt) {
      let refEntityConfig = this.config.refEntity;
      let idValue = selectOpt[refEntityConfig.idField];
      let labelValue = selectOpt[refEntityConfig.labelField];
      mapInputChange = this.mapSelect(ui, bean, selectOpt, idValue, labelValue);
      model.resetWithValue("");
    } else {
      mapInputChange = this.mapSelect(ui, bean, null, null, userInput);
      model.resetWithValue("");
    }
    if (mapInputChange && inputObserver) {
      let field = this.config.bean.labelField;
      inputObserver.getInputChangeCollector().addChange(field, oldVal, userInput);
    }
    //if (mapInputChange == 'success' && onPostUpdate) {
    if (onPostUpdate) {
      onPostUpdate(ui, bean, selectOpt || {}, userInput);
    }
    ui.forceUpdate();
  }

  mapSelect(ui: BBRefEntity, bean: any, selectOpt: any, idValue: any, labelValue: any) {
    let refEntities = bean as Array<any>;
    let beanConfig = this.config.bean;
    if (beanConfig.mapSelect) {
      return beanConfig.mapSelect(ui, refEntities, selectOpt, idValue, labelValue);
    }
    if (selectOpt) {
      let obj = { [beanConfig.idField]: idValue, [beanConfig.labelField]: labelValue }
      refEntities.push(obj);
      return 'success';
    } else {
      const { allowUserInput } = ui.props;
      if (allowUserInput) {
        let obj = { [beanConfig.labelField]: labelValue }
        refEntities.push(obj);
        return 'success';
      } else {
        return 'fail';
      }
    }
  }
}

export interface BBRefMultiEntityProps extends BBRefEntityProps {
}
export class BBRefMultiEntity<T extends BBRefMultiEntityProps = BBRefMultiEntityProps> extends BBRefEntity<T> {

  protected onPostCreatePlugin(plugin: BBRefEntityPlugin) {
    let refEntityConfig = plugin.config.refEntity;
    refEntityConfig.multiselect = true;
    let fields = refEntityConfig.vgridRecordConfig.fields;
    fields.splice(0, 0, DbEntityListConfigTool.FIELD_SELECTOR(true)[0]);
  }

  onKeyDown = (evt: KeyboardEvent) => {
    const { model } = this.state;
    let { bean } = this.props;
    let refEntities = bean as Array<any>;
    if (model.input == '' && evt.key === 'Backspace') {
      if (refEntities.length > 0) {
        this.onRemove(refEntities.length - 1);
      }
      evt.stopPropagation();
      return;
    }

    this.onKeyDownDefault(evt);
  }

  onSelectMultiRefEntitiesCallback = (_appCtx: AppContext, pageCtx: PageContext, selectOpts: Array<any>) => {
    const { model } = this.state;
    pageCtx.back();
    let onSelectCallback = () => {
      for (let selOpt of selectOpts) {
        this.plugin.addRefEntity(this, selOpt, model.input);
      }
    }
    setTimeout(onSelectCallback, 50);
  }

  onRemove(idx: number) {
    let { bean } = this.props;
    let refEntities = bean as Array<any>;
    setTimeout(() => {
      refEntities.splice(idx, 1);
      this.forceUpdate();
    }, 50);
  }

  showRefEntityInfo(refEntity: any) {
    let { appContext, pageContext, hideMoreInfo } = this.props;
    if (hideMoreInfo) return;

    let backend = this.plugin.config.backend
    if (!backend.loadMethod) {
      throw new Error(`the load method is not config.`)
    }
    let idValue = refEntity[this.plugin.config.bean.idField];
    if (!idValue || idValue <= 0) {
      let html = (<div>The id is not set</div>)
      dialogMessage('Ref Entity', 'warning', html);
      return;
    }
    let successCallback = (resp: BackendResponse) => {
      let refEntity = resp.data;
      let createAppPage = (appCtx: AppContext, pageCtx: PageContext) => {
        return (
          <UIRefEntityInfo appContext={appCtx} pageContext={pageCtx} config={this.plugin.config} refEntity={refEntity} />
        )
      }
      pageContext.createPopupPage('ref-entity', 'Ref Entity', createAppPage, { size: 'md' });
    }
    let params = { 'id': idValue };
    if (backend.createLoadParams) {
      params = backend.createLoadParams(idValue);
    }
    appContext
      .createBackendCall(backend.service, backend.loadMethod, params)
      .withSuccess(successCallback)
      .call();
  }

  renderToggle() {
    let { label, bean, disable } = this.props;
    // let refEntityConfig = this.plugin.config.refEntity;

    let beanConfig = this.plugin.config.bean;
    let refEntities = bean as Array<any>;
    let refEntityWidgets = [];
    for (let i = 0; i < refEntities.length; i++) {
      let bean = refEntities[i];
      let label = beanConfig.labelField ? bean[beanConfig.labelField] : bean[beanConfig.idField];
      let widget = (
        <div key={i} className="flex-hbox-grow-0 border border-dashed px-1 me-1">
          <Button laf='link' className='p-1' onClick={() => this.showRefEntityInfo(bean)}>
            {label}
          </Button>
          <Button hidden={disable} laf='link' className="text-danger border-start p-1" onClick={() => this.onRemove(i)} >
            <FeatherIcon.Trash size={12} />
          </Button>
        </div>
      );
      refEntityWidgets.push(widget);
    }

    let inputUI = (
      <div className='flex-vbox'>
        <div className='flex-hbox-grow-0 flex-wrap' >
          {refEntityWidgets}
        </div>
        <div id={`popover-trigger-${this.popoverId}`} className='flex-hbox py-1' style={{ minWidth: 150 }}>
          {this.renderInput()}
          {this.renderSearchRefEntities()}
        </div>
      </div>
    );

    if (!label) return inputUI;

    return (
      <div className='bb-field' >
        <label className='form-label'>
          {label}
        </label>
        {inputUI}
      </div>
    );
  }
}