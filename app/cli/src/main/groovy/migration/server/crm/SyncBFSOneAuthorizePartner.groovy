package migration.server.crm

import javax.sql.DataSource

import org.slf4j.Logger
import org.slf4j.LoggerFactory

import cloud.datatp.fforwarder.core.partner.BFSOnePartnerLogic
import cloud.datatp.fforwarder.core.partner.BFSOnePartnerService
import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartner
import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.company.entity.CompanyConfig
import net.datatp.module.data.db.ExternalDataSourceManager
import net.datatp.module.data.db.SqlMapRecord
import net.datatp.module.data.db.SqlQueryManager
import net.datatp.module.data.db.SqlSelectView
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.module.hr.EmployeeReadLogic
import net.datatp.module.hr.entity.Employee
import net.datatp.security.client.ClientContext
import net.datatp.util.ds.Collections
import net.datatp.util.text.StringUtil

class SyncAllBFSOnePartner extends ServiceRunnableSet {
    private static final Logger logger = LoggerFactory.getLogger(SyncAllBFSOnePartner.class);
    private static String label = "SYNC ALL BFSONE PARTNER";

    public SyncAllBFSOnePartner() {
        super(label);

        ServiceRunnable syncService = new ServiceRunnable(label) {
            @Override
            public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
                ClientContext client = scriptCtx.getClientCtx();
                BFSOnePartnerLogic bfsOnePartnerLogic = scriptCtx.getService(BFSOnePartnerLogic.class);
                EmployeeReadLogic employeeLogic = scriptCtx.getService(EmployeeReadLogic.class);
                BFSOnePartnerService bfsOnePartnerService = scriptCtx.getService(BFSOnePartnerService.class);
                SqlQueryManager sqlQueryManager = scriptCtx.getService(SqlQueryManager.class);
                CompanyConfig companyConfig = bfsOnePartnerLogic.getCompanyConfigLogic().getCompanyConfigByCompanyId(client, client.getCompanyId());
                ExternalDataSourceManager.DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds",
                        ExternalDataSourceManager.DataSourceParams.class);
                DataSource ds = bfsOnePartnerLogic.getDataSourceManager().getDataSource(client, dsPrams);

                final String dataDir = scriptCtx.getDataDir();
                String SCRIPT_DIR = dataDir + "/crm/sql";
                String fileName = "SyncBFSOnePartner.groovy";
                SqlQueryManager.QueryContext queryContext = sqlQueryManager.create(SCRIPT_DIR, fileName);
                Binding binding = new Binding();
                SqlSelectView view = queryContext.createSqlSelectView(ds, binding);
                List<SqlMapRecord> records = view.getSqlMapRecords();
                
                for (SqlMapRecord record : records) {
                    String salemanOwner = record.getString("sale_owner_code");
                    if (StringUtil.isEmpty(salemanOwner)) {
                        record.put("sale_owner_code", "NA");
                    }
                    String salemanAllocated = record.getString("saleman_obligation_code");
                    if (StringUtil.isEmpty(salemanAllocated)) {
                        record.put("saleman_obligation_code", "NA");
                    }
                }

                Map<String, List<BFSOnePartner>> salePartnerGroup = BFSOnePartner.groupBySaleObligation(records);
                for (String saleCode : salePartnerGroup.keySet()) {
                    List<BFSOnePartner> partners = salePartnerGroup.get(saleCode);
                    if (saleCode.equals("NA")) {
                        for (BFSOnePartner partner : partners) {
                            BFSOnePartner partnerInDb = bfsOnePartnerLogic.getByCode(client, partner.getBfsonePartnerCode());
                            if (partnerInDb == null) {
                                bfsOnePartnerService.saveBFSOnePartner(client, partner);
                            }
                        }
                    } else {
                        List<Employee> employees = employeeLogic.findEmployeeByBFSOneCode(client, saleCode);
                        if (Collections.isNotEmpty(employees) && employees.size() == 1) {
                            Employee employee = employees.get(0);
                            bfsOnePartnerService.importPartners(client, employee, partners);
                        } else {
                            logger.warn("Owner Saleman not found, code = {}", saleCode);
                            for (BFSOnePartner partner : partners) {
                                BFSOnePartner partnerInDb = bfsOnePartnerLogic.getByCode(client, partner.getBfsonePartnerCode());
                                if (partnerInDb == null) {
                                    bfsOnePartnerService.saveBFSOnePartner(client, partner);
                                }
                            }
                        }
                    }
                }

                Map<String, List<BFSOnePartner>> firstSalePartnerGroup = BFSOnePartner.groupBySaleOwnerObligation(records);
                for (String bfsoneCode : firstSalePartnerGroup.keySet()) {
                    List<BFSOnePartner> partners = firstSalePartnerGroup.get(bfsoneCode);
                    if (bfsoneCode.equals("NA")) {
                        for (BFSOnePartner partner : partners) {
                            BFSOnePartner partnerInDb = bfsOnePartnerLogic.getByCode(client, partner.getBfsonePartnerCode());
                            if (partnerInDb == null) {
                                bfsOnePartnerService.saveBFSOnePartner(client, partner);
                            }
                        }
                    } else {
                        List<Employee> employees = employeeLogic.findEmployeeByBFSOneCode(client, bfsoneCode);
                        if (Collections.isNotEmpty(employees) && employees.size() == 1) {
                            Employee employee = employees.get(0);
                            bfsOnePartnerService.importPartners(client, employee, partners);
                        } else {
                            logger.warn("Owner Saleman not found, code = {}", bfsoneCode);
                            for (BFSOnePartner partner : partners) {
                                BFSOnePartner partnerInDb = bfsOnePartnerLogic.getByCode(client, partner.getBfsonePartnerCode());
                                if (partnerInDb == null) {
                                    bfsOnePartnerService.saveBFSOnePartner(client, partner);
                                }
                            }
                        }
                    }
                }

                logger.info("sync {}", records.size());
            }
        };
        addRunnable(syncService);
    }
}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "V1_0_0_2024-06-19")

SyncAllBFSOnePartner syncPartner = new SyncAllBFSOnePartner();
syncPartner.run(reporter, scriptCtx);

return "DONE!!!"