package crm.sql

def buildKeyAccountReportQuery() {
    String query = """
        SELECT
          p.PartnerID                   AS partner_code,
          p.DateCreate                  AS date_created,
          p.DateModify                  AS date_modified,
          p.PartnerName                 AS name,
          p.PartnerName2                AS label,
          p.PartnerName3                AS localized_label,
          p.Address                     AS address,
          p.Address2                    AS localized_address,
          p.PersonalContact             AS personal_contact,
          p.Email                       AS email,
          p.Fax                         AS fax,
          p.Cell                        AS cell,
          p.Homephone                   AS home_phone,
          p.Workphone                   AS work_phone,
          p.Taxcode                     AS tax_code,
          p.Country                     AS country_label,
          p.VIP                         AS "source",
          p.BankAccsNo                  AS bank_accs_no,
          p.BankName                    AS bank_name,
          p.BankAddress                 AS bank_address,
          p.Location                    AS "scope",
          p.Industry                    AS industry_code,
          i.IndustryName                AS industry_label,
          p.NotesLess                   AS note_1,
          p.Notes                       AS note_2,

          -- first sale employee
          p.ContactID                   AS sale_owner_code,
          ui.Username                   AS sale_owner_username,
          o.ContactID                   AS saleman_obligation_code,

          -- input employee
          p.InputPeople                 AS input_username,

          p.Category                    AS category,
          p.[Group]                     AS "group",
          p.CustomertypeID              AS customer_type_id
        FROM Partners p
          LEFT JOIN lst_Industries i ON i.IDKey = p.Industry
          LEFT JOIN UserInfos ui ON ui.UserID = p.ContactID
          LEFT JOIN ContactPartnerObligation o ON p.PartnerID = o.PartnerID
        WHERE 1 = 1 and p.[Group] = 'COLOADERS' and p.Category is not null and p.Category <> '' 
    """
    return query;
}

buildKeyAccountReportQuery();